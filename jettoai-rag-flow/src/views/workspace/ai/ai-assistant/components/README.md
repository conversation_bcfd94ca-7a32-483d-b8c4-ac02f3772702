# AssistantCarousel 组件

一个功能完整的应用助手轮播组件，支持触摸滑动、键盘导航和响应式设计。

## 功能特性

- ✅ **默认展示4个助手卡片**
- ✅ **左右切换按钮** - 支持鼠标点击切换
- ✅ **平滑动画效果** - 使用CSS transform实现流畅过渡
- ✅ **响应式设计** - 根据屏幕大小自动调整显示数量
- ✅ **触摸支持** - 支持移动端滑动手势
- ✅ **键盘导航** - 支持方向键、Home、End键
- ✅ **自动播放** - 可选的自动轮播功能
- ✅ **加载状态** - 骨架屏加载效果
- ✅ **指示器** - 显示当前页面位置

## 使用方法

```vue
<template>
  <AssistantCarousel 
    :assistant-list="assistantList" 
    :loading="loading"
    :items-per-page="4"
    :show-indicators="true"
    :auto-play="false"
    :auto-play-interval="3000"
    @create="handleCreate"
  />
</template>

<script setup>
import AssistantCarousel from './components/AssistantCarousel.vue'

const assistantList = ref([
  {
    id: '1',
    name: '智能助手1',
    description: '这是一个智能助手的描述',
    icon: '/path/to/icon.png',
    updateTime: '2024-01-01',
    count: '10',
    version: 'V1.0',
    type: '通用'
  }
  // ... 更多助手数据
])

const loading = ref(false)

const handleCreate = () => {
  console.log('创建新助手')
}
</script>
```

## Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| assistantList | Assistant[] | [] | 助手列表数据 |
| itemsPerPage | number | 4 | 每页显示的项目数量 |
| showIndicators | boolean | true | 是否显示指示器 |
| autoPlay | boolean | false | 是否自动播放 |
| autoPlayInterval | number | 3000 | 自动播放间隔(毫秒) |
| loading | boolean | false | 是否显示加载状态 |

## Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| create | 点击创建按钮时触发 | - |

## Assistant 数据结构

```typescript
interface Assistant {
  id: string          // 唯一标识
  name: string        // 助手名称
  description: string // 助手描述
  icon: string        // 图标路径
  updateTime: string  // 更新时间
  count: string       // 用例数量
  version: string     // 版本号
  type: string        // 类型
}
```

## 响应式断点

- **桌面端 (>1200px)**: 显示4个项目
- **平板端 (768px-1200px)**: 显示3个项目  
- **手机端 (480px-768px)**: 显示2个项目
- **小屏手机 (<480px)**: 显示1个项目

## 键盘快捷键

- `←` 左箭头: 上一页
- `→` 右箭头: 下一页
- `Home`: 跳转到第一页
- `End`: 跳转到最后一页

## 触摸手势

- **向左滑动**: 下一页
- **向右滑动**: 上一页
- **最小滑动距离**: 50px

## 样式定制

组件使用CSS变量，可以通过覆盖以下变量来定制样式：

```css
:root {
  --color-fill-1: #ffffff;
  --color-border-2: #e5e6eb;
  --color-bg-1: #ffffff;
  --color-text-2: #4e5969;
  --color-primary: #5147ff;
  --color-primary-light-4: #f2f1ff;
}
```

## 注意事项

1. 确保传入的 `assistantList` 数据结构正确
2. 组件会自动处理空数据状态
3. 触摸事件仅在移动设备上生效
4. 自动播放会在用户交互时暂停
5. 加载状态会显示骨架屏效果
