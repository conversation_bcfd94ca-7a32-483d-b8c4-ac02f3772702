<template>
  <div class="assistant-carousel">
    <!-- 轮播容器 -->
    <div ref="carouselContainer" class="carousel-container">
      <!-- 左侧切换按钮 -->
      <button
        v-if="showPrevButton"
        class="carousel-btn carousel-btn--prev"
        :disabled="currentIndex === 0"
        @click="handlePrev"
      >
        <icon-left />
      </button>

      <!-- 轮播内容区域 -->
      <div ref="carouselContent" class="carousel-content" tabindex="0" @keydown="handleKeyDown">
        <!-- 加载状态 -->
        <div v-if="loading" class="carousel-track">
          <div
            v-for="n in currentItemsPerPage"
            :key="`skeleton-${n}`"
            class="carousel-item skeleton-item"
            :style="itemStyle"
          >
            <div class="skeleton-card">
              <div class="skeleton-header">
                <div class="skeleton-avatar" />
                <div class="skeleton-info">
                  <div class="skeleton-title" />
                  <div class="skeleton-subtitle" />
                </div>
              </div>
              <div class="skeleton-content">
                <div class="skeleton-line" />
                <div class="skeleton-line short" />
              </div>
              <div class="skeleton-footer">
                <div class="skeleton-button" />
              </div>
            </div>
          </div>
        </div>

        <!-- 正常内容 -->
        <div v-else ref="carouselTrack" class="carousel-track" :style="trackStyle">
          <!-- 创建智能体卡片 -->
          <div class="carousel-item create-item" :style="itemStyle">
            <div
              class="create bg-[var(--color-fill-1)] border-[var(--color-border-2)] border-[1px] border-dashed rounded-lg p-4"
              @click="handleCreate"
            >
              <div class="create-content">
                <div class="create-icon">
                  <icon-plus />
                </div>
                <div class="create-title">创建智能体</div>
                <div class="create-desc">选择应用模版，使用该模版创建应用到对应的空间</div>
              </div>
            </div>
          </div>

          <!-- 助手卡片 -->
          <div
            v-for="(assistant, index) in assistantList"
            :key="assistant.id || index"
            class="carousel-item"
            :style="itemStyle"
          >
            <AssistantCard :assistant="assistant" />
          </div>
        </div>
      </div>
      <!-- 右侧切换按钮 -->
      <button
        v-if="showNextButton"
        class="carousel-btn carousel-btn--next"
        :disabled="currentIndex >= maxIndex"
        @click="handleNext"
      >
        <icon-right />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import AssistantCard from './AssistantCard.vue'

interface Assistant {
  id: string
  name: string
  description: string
  icon: string
  updateTime: string
  count: string
  version: string
  type: string
}

interface Props {
  assistantList: Assistant[]
  itemsPerPage?: number
  showIndicators?: boolean
  autoPlay?: boolean
  autoPlayInterval?: number
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  itemsPerPage: 4,
  showIndicators: true,
  autoPlay: false,
  autoPlayInterval: 3000,
  loading: false
})

const emit = defineEmits<{
  create: []
}>()

// 响应式数据
const carouselContainer = ref<HTMLElement>()
const carouselContent = ref<HTMLElement>()
const carouselTrack = ref<HTMLElement>()
const currentIndex = ref(0)
const itemWidth = ref(0)
const containerWidth = ref(0)
const currentItemsPerPage = ref(props.itemsPerPage)

// 计算属性
const totalItems = computed(() => props.assistantList.length + 1) // +1 for create card
const maxIndex = computed(() => Math.max(0, totalItems.value - currentItemsPerPage.value))
const indicatorCount = computed(() => Math.ceil(totalItems.value / currentItemsPerPage.value))

const showPrevButton = computed(() => totalItems.value > currentItemsPerPage.value)
const showNextButton = computed(() => totalItems.value > currentItemsPerPage.value)

const trackStyle = computed(() => {
  const translateX = -(currentIndex.value * itemWidth.value)
  return {
    transform: `translateX(${translateX}px)`,
    transition: 'transform 0.3s ease-in-out'
  }
})

const itemStyle = computed(() => {
  const gapSize = 24
  const itemWidthPercent = 100 / currentItemsPerPage.value
  const gapAdjustment = (gapSize * (currentItemsPerPage.value - 1)) / currentItemsPerPage.value
  return {
    flex: `0 0 calc(${itemWidthPercent}% - ${gapAdjustment}px)`
  }
})

// 方法
const calculateDimensions = () => {
  if (!carouselContent.value) return

  containerWidth.value = carouselContent.value.offsetWidth

  // 根据容器宽度动态计算每页显示的项目数量
  if (containerWidth.value <= 480) {
    currentItemsPerPage.value = 1
  } else if (containerWidth.value <= 768) {
    currentItemsPerPage.value = 2
  } else if (containerWidth.value <= 1200) {
    currentItemsPerPage.value = 3
  } else {
    currentItemsPerPage.value = props.itemsPerPage
  }

  itemWidth.value = containerWidth.value / currentItemsPerPage.value
}

const handlePrev = () => {
  if (currentIndex.value > 0) {
    currentIndex.value = Math.max(0, currentIndex.value - 1)
  }
}

const handleNext = () => {
  if (currentIndex.value < maxIndex.value) {
    currentIndex.value = Math.min(maxIndex.value, currentIndex.value + 1)
  }
}

const handleCreate = () => {
  emit('create')
}

// 键盘事件处理
const handleKeyDown = (e: KeyboardEvent) => {
  switch (e.key) {
    case 'ArrowLeft':
      e.preventDefault()
      handlePrev()
      break
    case 'ArrowRight':
      e.preventDefault()
      handleNext()
      break
    case 'Home':
      e.preventDefault()
      currentIndex.value = 0
      break
    case 'End':
      e.preventDefault()
      currentIndex.value = maxIndex.value
      break
  }
}

// 自动播放
let autoPlayTimer: NodeJS.Timeout | null = null

const startAutoPlay = () => {
  if (!props.autoPlay) return

  autoPlayTimer = setInterval(() => {
    if (currentIndex.value >= maxIndex.value) {
      currentIndex.value = 0
    } else {
      handleNext()
    }
  }, props.autoPlayInterval)
}

const stopAutoPlay = () => {
  if (autoPlayTimer) {
    clearInterval(autoPlayTimer)
    autoPlayTimer = null
  }
}

// 响应式处理
const handleResize = () => {
  calculateDimensions()
  // 确保当前索引不超出范围
  if (currentIndex.value > maxIndex.value) {
    currentIndex.value = maxIndex.value
  }
}

// 生命周期
onMounted(async () => {
  await nextTick()
  calculateDimensions()
  startAutoPlay()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  stopAutoPlay()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.assistant-carousel {
  position: relative;
  width: 100%;
}

.carousel-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 16px;
}

.carousel-content {
  flex: 1;
  overflow: hidden;
  border-radius: 8px;
  outline: none; /* 移除焦点轮廓 */
  touch-action: pan-y; /* 允许垂直滚动，禁止水平滚动 */
}

.carousel-track {
  display: flex;
  gap: 24px;
  will-change: transform;
}

.carousel-item {
  flex: 0 0 calc(25% - 18px); /* 默认4个项目，减去gap */
  min-width: 0;
}

.carousel-item.create-item {
  flex: 0 0 calc(25% - 18px);
}

.carousel-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--color-border-2);
  border-radius: 50%;
  background-color: var(--color-bg-1);
  color: var(--color-text-2);
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 2;
}

.carousel-btn:hover:not(:disabled) {
  background-color: var(--color-primary-light-4);
  border-color: var(--color-primary);
  color: var(--color-primary);
  box-shadow: 0 2px 8px rgba(81, 71, 255, 0.2);
}

.carousel-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

/* 创建卡片样式 */
.create {
  cursor: pointer;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.create:hover {
  box-shadow: 0px 2px 8px 0px rgba(81, 71, 255, 0.2);
  border: 1px solid #aad4ff;
}

.create-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
}

.create-icon {
  width: 33px;
  height: 33px;
  border-radius: 50%;
  background-color: var(--color-fill-2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  color: var(--color-text-1);
}

.create-icon:hover {
  background-color: var(--color-primary-light-4);
  color: var(--color-primary);
}

.create-title {
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 4px;
}

.create-desc {
  font-size: 13px;
  color: var(--color-text-3);
  line-height: 1.5;
}

/* 骨架屏样式 */
.skeleton-item {
  min-width: 0;
}

.skeleton-card {
  background-color: #ffffff;
  border: 1px solid var(--color-border-2);
  border-radius: 8px;
  padding: 16px;
  height: 200px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.skeleton-header {
  display: flex;
  gap: 12px;
}

.skeleton-avatar {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-title {
  height: 16px;
  width: 60%;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-subtitle {
  height: 12px;
  width: 40%;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-line {
  height: 12px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-line.short {
  width: 70%;
}

.skeleton-footer {
  display: flex;
  justify-content: center;
}

.skeleton-button {
  width: 80px;
  height: 24px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .carousel-item,
  .carousel-item.create-item {
    flex: 0 0 calc(33.333% - 16px); /* 3个项目 */
  }
}

@media (max-width: 768px) {
  .carousel-item,
  .carousel-item.create-item {
    flex: 0 0 calc(50% - 12px); /* 2个项目 */
  }

  .carousel-track {
    gap: 16px;
  }

  .carousel-container {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .carousel-item,
  .carousel-item.create-item {
    flex: 0 0 100%; /* 1个项目 */
  }

  .carousel-track {
    gap: 12px;
  }
}
</style>
